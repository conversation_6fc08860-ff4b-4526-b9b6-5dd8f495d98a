package com.asmtunis.procaisseinventory.core.session

import android.util.Log
import com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.ACTIVE_SESSION_ID
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SESSION_CAISSE_CODE
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SESSION_FOND_CAISSE
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SESSION_IS_ACTIVE
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SESSION_OPENING_DATE
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SESSION_USER_ID
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.SessionCaisse
import com.asmtunis.procaisseinventory.data.sessioncaisse.local.repository.SessionCaisseLocalRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import javax.inject.Inject
import javax.inject.Named
import javax.inject.Singleton

/**
 * Singleton SessionManager that handles all session-related operations including
 * validation, expiration checking, and state management for ProCaisse Mobile.
 * 
 * This class implements sophisticated cash register session management that mirrors
 * traditional POS operations with comprehensive digital tracking and validation mechanisms.
 */
@Singleton
class SessionManager @Inject constructor(
    private val dataStoreRepository: DataStoreRepository,
    private val sessionCaisseLocalRepository: SessionCaisseLocalRepository
) {
    companion object {
        private const val TAG = "SessionManager"
        private const val DATE_FORMAT = "yyyy-MM-dd HH:mm:ss"
        private const val DATE_ONLY_FORMAT = "yyyy-MM-dd"
    }

    /**
     * Flow that emits the current session state by combining DataStore preferences
     * with local database session data for real-time session monitoring
     */
    val currentSessionState: Flow<SessionState> = combine(
        dataStoreRepository.getBoolean(SESSION_IS_ACTIVE, false),
        dataStoreRepository.getString(ACTIVE_SESSION_ID, ""),
        dataStoreRepository.getString(SESSION_OPENING_DATE, ""),
        dataStoreRepository.getString(SESSION_USER_ID, ""),
        dataStoreRepository.getString(SESSION_CAISSE_CODE, ""),
        dataStoreRepository.getString(SESSION_FOND_CAISSE, "")
    ) { flows ->
        val isActive = flows[0] as Boolean
        val sessionId = flows[1] as String
        val openingDate = flows[2] as String
        val userId = flows[3] as String
        val caisseCode = flows[4] as String
        val fondCaisse = flows[5] as String

        SessionState(
            isActive = isActive,
            sessionId = sessionId,
            openingDate = openingDate,
            userId = userId,
            caisseCode = caisseCode,
            fondCaisse = fondCaisse
        )
    }

    /**
     * Validates the current session for operation execution.
     * Implements sophisticated business logic checking multiple conditions:
     * - Active session exists
     * - Session hasn't expired (date comparison)
     * - Session remains open in database (sCClotCaisse = 0)
     * 
     * @return SessionValidationResult indicating the validation status
     */
    suspend fun validateSessionForOperation(): SessionValidationResult {
        try {
            val sessionState = currentSessionState.first()
            
            // Check if session is active in DataStore
            if (!sessionState.isActive || sessionState.sessionId.isEmpty()) {
                Log.d(TAG, "No active session found in DataStore")
                return SessionValidationResult.NO_ACTIVE_SESSION
            }

            // Check session expiration based on date comparison
            if (isSessionExpired(sessionState.openingDate)) {
                Log.d(TAG, "Session expired: ${sessionState.openingDate}")
                return SessionValidationResult.SESSION_EXPIRED
            }

            // Verify session status in database
            val sessionCaisseList = sessionCaisseLocalRepository.getAll().first()
            val currentSession = sessionCaisseList.find { it.sCIdSCaisse == sessionState.sessionId }
            
            if (currentSession == null) {
                Log.d(TAG, "Session not found in database: ${sessionState.sessionId}")
                return SessionValidationResult.NO_ACTIVE_SESSION
            }

            // Check if session is closed in database
            if (currentSession.sCClotCaisse == 1) {
                Log.d(TAG, "Session is closed in database: ${sessionState.sessionId}")
                return SessionValidationResult.SESSION_CLOSED_IN_DB
            }

            Log.d(TAG, "Session validation successful: ${sessionState.sessionId}")
            return SessionValidationResult.VALID
            
        } catch (e: Exception) {
            Log.e(TAG, "Error validating session", e)
            return SessionValidationResult.NO_ACTIVE_SESSION
        }
    }

    /**
     * Checks if a session is expired based on date comparison.
     * Sessions are considered expired if the current date differs from the session opening date.
     * Implements traditional POS pattern where sessions expire at midnight.
     * 
     * @param sessionOpeningDate The session opening date in "yyyy-MM-dd HH:mm:ss" format
     * @return true if session is expired, false otherwise
     */
    fun isSessionExpired(sessionOpeningDate: String): Boolean {
        if (sessionOpeningDate.isEmpty()) return true
        
        try {
            val dateFormat = SimpleDateFormat(DATE_ONLY_FORMAT, Locale.getDefault())
            val currentDate = dateFormat.format(Date())
            
            // Extract date part from session opening datetime
            val sessionDate = if (sessionOpeningDate.length >= 10) {
                sessionOpeningDate.substring(0, 10)
            } else {
                return true
            }
            
            return currentDate != sessionDate
        } catch (e: Exception) {
            Log.e(TAG, "Error checking session expiration", e)
            return true
        }
    }

    /**
     * Stores session data in DataStore preferences for rapid session state retrieval
     * throughout the application lifecycle.
     * 
     * @param sessionCaisse The session data to store
     */
    suspend fun storeSessionInDataStore(sessionCaisse: SessionCaisse) {
        try {
            dataStoreRepository.putString(ACTIVE_SESSION_ID, sessionCaisse.sCIdSCaisse)
            dataStoreRepository.putString(SESSION_OPENING_DATE, sessionCaisse.sCDateHeureOuv ?: "")
            dataStoreRepository.putString(SESSION_USER_ID, sessionCaisse.sCCodeUtilisateur.toString())
            dataStoreRepository.putString(SESSION_CAISSE_CODE, sessionCaisse.sCCaisse ?: "")
            dataStoreRepository.putString(SESSION_FOND_CAISSE, sessionCaisse.sCFondCaisse ?: "")
            dataStoreRepository.putBoolean(SESSION_IS_ACTIVE, true)
            
            Log.d(TAG, "Session stored in DataStore: ${sessionCaisse.sCIdSCaisse}")
        } catch (e: Exception) {
            Log.e(TAG, "Error storing session in DataStore", e)
        }
    }

    /**
     * Clears all session data from DataStore preferences.
     * Used during session closure to ensure clean state reset.
     */
    suspend fun clearSessionFromDataStore() {
        try {
            dataStoreRepository.putString(ACTIVE_SESSION_ID, "")
            dataStoreRepository.putString(SESSION_OPENING_DATE, "")
            dataStoreRepository.putString(SESSION_USER_ID, "")
            dataStoreRepository.putString(SESSION_CAISSE_CODE, "")
            dataStoreRepository.putString(SESSION_FOND_CAISSE, "")
            dataStoreRepository.putBoolean(SESSION_IS_ACTIVE, false)
            
            Log.d(TAG, "Session cleared from DataStore")
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing session from DataStore", e)
        }
    }

    /**
     * Gets the current active session from local database
     * 
     * @return SessionCaisse if active session exists, null otherwise
     */
    suspend fun getCurrentActiveSession(): SessionCaisse? {
        return try {
            val sessionState = currentSessionState.first()
            if (!sessionState.isActive || sessionState.sessionId.isEmpty()) {
                return null
            }
            
            val sessionCaisseList = sessionCaisseLocalRepository.getAll().first()
            sessionCaisseList.find { it.sCIdSCaisse == sessionState.sessionId }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting current active session", e)
            null
        }
    }

    /**
     * Checks if there is an active session
     * 
     * @return true if active session exists, false otherwise
     */
    suspend fun hasActiveSession(): Boolean {
        return try {
            val sessionState = currentSessionState.first()
            sessionState.isActive && sessionState.sessionId.isNotEmpty()
        } catch (e: Exception) {
            Log.e(TAG, "Error checking active session", e)
            false
        }
    }
}
