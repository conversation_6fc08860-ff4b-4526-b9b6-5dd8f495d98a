package com.asmtunis.procaisseinventory.core.session

import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.AddSessionCaisseResponse
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.SessionActivation
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.SessionCaisse
import com.asmtunis.procaisseinventory.data.sessioncaisse.local.repository.SessionCaisseLocalRepository
import com.asmtunis.procaisseinventory.data.sessioncaisse.remote.api.SessionCaisseApi
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.util.UUID
import javax.inject.Inject

/**
 * ViewModel that orchestrates the comprehensive session workflow for ProCaisse Mobile.
 * Handles session creation, validation, closure, and state management with sophisticated
 * business logic that mirrors traditional POS operations.
 */
@HiltViewModel
class SessionWorkflowViewModel @Inject constructor(
    private val sessionManager: SessionManager,
    private val sessionCaisseApi: SessionCaisseApi,
    private val sessionCaisseLocalRepository: SessionCaisseLocalRepository
) : ViewModel() {

    companion object {
        private const val TAG = "SessionWorkflowViewModel"
    }

    // Session creation state
    var createSessionState: RemoteResponseState<AddSessionCaisseResponse> by mutableStateOf(RemoteResponseState())
        private set

    // Session closure state
    var closeSessionState: RemoteResponseState<Boolean> by mutableStateOf(RemoteResponseState())
        private set

    // Current session validation state
    var sessionValidationState: SessionValidationResult by mutableStateOf(SessionValidationResult.NO_ACTIVE_SESSION)
        private set

    // Fond caisse input dialog state
    var showFondCaisseDialog: Boolean by mutableStateOf(false)
        private set

    // Session validation dialog state
    var showSessionValidationDialog: Boolean by mutableStateOf(false)
        private set

    // Fond caisse amount
    var fondCaisseAmount: String by mutableStateOf("")
        private set

    /**
     * Initiates the session creation workflow by showing the FondCaisseInputView dialog.
     * This begins the structured opening process that requires an initial cash fund amount.
     */
    fun startSessionCreation() {
        fondCaisseAmount = ""
        showFondCaisseDialog = true
    }

    /**
     * Updates the fond caisse amount entered by the user
     */
    fun updateFondCaisseAmount(amount: String) {
        fondCaisseAmount = amount
    }

    /**
     * Dismisses the fond caisse input dialog
     */
    fun dismissFondCaisseDialog() {
        showFondCaisseDialog = false
        fondCaisseAmount = ""
    }

    /**
     * Creates a new session with the specified parameters.
     * Implements the comprehensive session creation process including:
     * - SessionActivation object construction
     * - GenericObject wrapping with base configuration
     * - API call to Creation_Session_vendeur stored procedure
     * - Local and DataStore persistence
     * 
     * @param baseConfig Base configuration for API calls
     * @param utilisateur Current user information
     * @param androidId Device identifier
     */
    fun createSession(
        baseConfig: BaseConfig,
        utilisateur: Utilisateur,
        androidId: String
    ) {
        if (fondCaisseAmount.isEmpty() || fondCaisseAmount.toDoubleOrNull() == null || fondCaisseAmount.toDouble() <= 0) {
            Log.w(TAG, "Invalid fond caisse amount: $fondCaisseAmount")
            return
        }

        viewModelScope.launch {
            try {
                createSessionState = RemoteResponseState(loading = true)

                // Generate unique session ID
                val sessionId = UUID.randomUUID().toString()

                // Create SessionActivation object with critical session parameters
                val sessionActivation = SessionActivation(
                    caisse = utilisateur.Code_Caisse,
                    utilisateur = utilisateur.codeUt,
                    carnet = utilisateur.Code_Carnet,
                    station = utilisateur.Station,
                    fondCaisse = fondCaisseAmount,
                    nomMachine = androidId
                )

                // Wrap in GenericObject with base configuration
                val baseConfigObj = GenericObject(
                    baseConfig,
                    Json.encodeToJsonElement(sessionActivation)
                )

                // Call API to create session
                val createSessionDeferred = async {
                    sessionCaisseApi.addSession(
                        Json.encodeToString(baseConfigObj),
                        sCIdSCaisse = sessionId
                    )
                }

                createSessionDeferred.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            val response = result.data
                            if (response != null && !response.error && response.sessionCaisse != null) {
                                // Store session in local database
                                sessionCaisseLocalRepository.upsert(response.sessionCaisse)
                                
                                // Store session in DataStore for rapid access
                                sessionManager.storeSessionInDataStore(response.sessionCaisse)
                                
                                createSessionState = RemoteResponseState(
                                    data = response,
                                    loading = false,
                                    error = null
                                )
                                
                                // Close dialog and reset state
                                dismissFondCaisseDialog()
                                
                                Log.d(TAG, "Session created successfully: ${response.sessionCaisse.sCIdSCaisse}")
                            } else {
                                val errorMessage = response?.message ?: "Unknown error creating session"
                                createSessionState = RemoteResponseState(
                                    data = null,
                                    loading = false,
                                    error = errorMessage
                                )
                                Log.e(TAG, "Session creation failed: $errorMessage")
                            }
                        }
                        is DataResult.Loading -> {
                            createSessionState = RemoteResponseState(loading = true)
                        }
                        is DataResult.Error -> {
                            createSessionState = RemoteResponseState(
                                data = null,
                                loading = false,
                                error = result.message
                            )
                            Log.e(TAG, "Session creation error: ${result.message}")
                        }
                    }
                }.collect { }

            } catch (e: Exception) {
                createSessionState = RemoteResponseState(
                    data = null,
                    loading = false,
                    error = e.message ?: "Unknown error"
                )
                Log.e(TAG, "Exception creating session", e)
            }
        }
    }

    /**
     * Validates the current session for operation execution.
     * Checks multiple conditions and updates validation state.
     */
    fun validateCurrentSession() {
        viewModelScope.launch {
            sessionValidationState = sessionManager.validateSessionForOperation()
            
            if (sessionValidationState != SessionValidationResult.VALID) {
                showSessionValidationDialog = true
            }
        }
    }

    /**
     * Closes the current session with comprehensive cleanup operations.
     * Implements the session closure workflow including:
     * - API call to closeSessionVendeur with facture flag
     * - Backend calculations of session totals
     * - DataStore cleanup
     * - Local database updates
     * 
     * @param baseConfig Base configuration for API calls
     */
    fun closeCurrentSession(baseConfig: BaseConfig) {
        viewModelScope.launch {
            try {
                closeSessionState = RemoteResponseState(loading = true)

                val closeSessionDeferred = async {
                    sessionCaisseApi.closeSessionVendeur(
                        baseConfig = Json.encodeToString(GenericObject(baseConfig, null)),
                        facture = true
                    )
                }

                closeSessionDeferred.await().onEach { result ->
                    when (result) {
                        is DataResult.Success -> {
                            if (result.data == true) {
                                // Clear session from DataStore
                                sessionManager.clearSessionFromDataStore()
                                
                                closeSessionState = RemoteResponseState(
                                    data = true,
                                    loading = false,
                                    error = null
                                )
                                
                                // Hide validation dialog
                                showSessionValidationDialog = false
                                
                                Log.d(TAG, "Session closed successfully")
                            } else {
                                closeSessionState = RemoteResponseState(
                                    data = false,
                                    loading = false,
                                    error = "Failed to close session"
                                )
                                Log.e(TAG, "Session closure failed")
                            }
                        }
                        is DataResult.Loading -> {
                            closeSessionState = RemoteResponseState(loading = true)
                        }
                        is DataResult.Error -> {
                            closeSessionState = RemoteResponseState(
                                data = false,
                                loading = false,
                                error = result.message
                            )
                            Log.e(TAG, "Session closure error: ${result.message}")
                        }
                    }
                }.collect { }

            } catch (e: Exception) {
                closeSessionState = RemoteResponseState(
                    data = false,
                    loading = false,
                    error = e.message ?: "Unknown error"
                )
                Log.e(TAG, "Exception closing session", e)
            }
        }
    }

    /**
     * Dismisses the session validation dialog
     */
    fun dismissSessionValidationDialog() {
        showSessionValidationDialog = false
    }

    /**
     * Resets the create session state
     */
    fun resetCreateSessionState() {
        createSessionState = RemoteResponseState()
    }

    /**
     * Resets the close session state
     */
    fun resetCloseSessionState() {
        closeSessionState = RemoteResponseState()
    }
}
