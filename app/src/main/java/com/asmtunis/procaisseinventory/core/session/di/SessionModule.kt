package com.asmtunis.procaisseinventory.core.session.di

import com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository
import com.asmtunis.procaisseinventory.core.session.SessionManager
import com.asmtunis.procaisseinventory.data.sessioncaisse.local.repository.SessionCaisseLocalRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton

/**
 * Hilt module for providing session management dependencies.
 * Ensures SessionManager is available as a singleton throughout the application.
 */
@Module
@InstallIn(SingletonComponent::class)
object SessionModule {

    @Provides
    @Singleton
    fun provideSessionManager(
        dataStoreRepository: DataStoreRepository,
        @Named("SessionCaisse") sessionCaisseLocalRepository: SessionCaisseLocalRepository
    ): SessionManager {
        return SessionManager(dataStoreRepository, sessionCaisseLocalRepository)
    }
}
