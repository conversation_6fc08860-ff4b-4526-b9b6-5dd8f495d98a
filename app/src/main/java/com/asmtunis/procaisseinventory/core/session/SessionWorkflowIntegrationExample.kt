package com.asmtunis.procaisseinventory.core.session

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Stop
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur

/**
 * Example integration showing how to use the comprehensive session workflow
 * in existing screens. This demonstrates the complete session management
 * lifecycle integration for ProCaisse Mobile.
 * 
 * This example shows:
 * 1. Session state monitoring
 * 2. Session validation before operations
 * 3. Session creation workflow
 * 4. Session closure workflow
 * 5. Real-time session information display
 */
@Composable
fun SessionWorkflowIntegrationExample(
    baseConfig: BaseConfig,
    utilisateur: Utilisateur,
    onOperationRequested: () -> Unit = {},
    sessionManager: SessionManager,
    sessionWorkflowViewModel: SessionWorkflowViewModel = hiltViewModel()
) {
    // Observe current session state
    val currentSessionState by sessionManager.currentSessionState.collectAsState(
        initial = SessionState()
    )

    // Session workflow integration
    SessionWorkflowComposable(
        baseConfig = baseConfig,
        utilisateur = utilisateur,
        onSessionValidated = {
            // Proceed with the requested operation
            onOperationRequested()
        },
        onSessionCreated = {
            // Handle session creation success
            // Could show success message, refresh UI, etc.
        },
        onSessionClosed = {
            // Handle session closure success
            // Could navigate to login, show message, etc.
        },
        sessionWorkflowViewModel = sessionWorkflowViewModel
    )

    // UI showing session information and controls
    SessionInfoCard(
        sessionState = currentSessionState,
        onStartSession = {
            sessionWorkflowViewModel.initiateSessionCreation()
        },
        onCloseSession = {
            sessionWorkflowViewModel.initiateSessionClosure(baseConfig)
        },
        onValidateAndProceed = {
            // This will validate session and show appropriate dialogs if needed
            sessionWorkflowViewModel.validateSessionForOperation { validationResult ->
                if (validationResult == SessionValidationResult.VALID) {
                    onOperationRequested()
                }
                // If validation fails, appropriate dialogs will be shown automatically
            }
        }
    )
}

/**
 * Card component displaying current session information and providing
 * session management controls.
 */
@Composable
private fun SessionInfoCard(
    sessionState: SessionState,
    onStartSession: () -> Unit,
    onCloseSession: () -> Unit,
    onValidateAndProceed: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "État de la session",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            if (sessionState.isActive) {
                // Active session information
                SessionActiveInfo(sessionState = sessionState)
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Session controls for active session
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Button(
                        onClick = onValidateAndProceed,
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(
                            imageVector = Icons.Default.PlayArrow,
                            contentDescription = null
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Continuer")
                    }
                    
                    OutlinedButton(
                        onClick = onCloseSession,
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Stop,
                            contentDescription = null
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Clôturer")
                    }
                }
            } else {
                // No active session
                SessionInactiveInfo()
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Button(
                    onClick = onStartSession,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(stringResource(R.string.create_session))
                }
            }
        }
    }
}

/**
 * Component showing active session information
 */
@Composable
private fun SessionActiveInfo(sessionState: SessionState) {
    Column {
        SessionInfoRow(
            label = "ID Session:",
            value = sessionState.sessionId.take(8) + "..."
        )
        
        SessionInfoRow(
            label = "Date d'ouverture:",
            value = SessionUtils.extractDateFromDateTime(sessionState.openingDate)
        )
        
        SessionInfoRow(
            label = "Heure d'ouverture:",
            value = SessionUtils.extractTimeFromDateTime(sessionState.openingDate)
        )
        
        SessionInfoRow(
            label = "Caisse:",
            value = sessionState.caisseCode
        )
        
        SessionInfoRow(
            label = "Fonds de caisse:",
            value = "${sessionState.fondCaisse} DT"
        )
        
        if (sessionState.openingDate.isNotEmpty()) {
            SessionInfoRow(
                label = "Durée:",
                value = SessionUtils.formatSessionDuration(sessionState.openingDate)
            )
        }
    }
}

/**
 * Component showing inactive session information
 */
@Composable
private fun SessionInactiveInfo() {
    Text(
        text = "Aucune session active. Vous devez créer une session pour commencer vos opérations de caisse.",
        style = MaterialTheme.typography.bodyMedium,
        color = MaterialTheme.colorScheme.onSurfaceVariant
    )
}

/**
 * Helper component for displaying session information rows
 */
@Composable
private fun SessionInfoRow(
    label: String,
    value: String
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodySmall,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * Extension function to easily integrate session validation into existing operations.
 * 
 * Usage example in existing ViewModels:
 * ```
 * fun performCriticalOperation() {
 *     sessionWorkflowViewModel.validateSessionBeforeOperation { isValid ->
 *         if (isValid) {
 *             // Proceed with operation
 *             executeCriticalBusinessLogic()
 *         }
 *         // If not valid, appropriate dialogs will be shown automatically
 *     }
 * }
 * ```
 */
fun SessionWorkflowViewModel.validateSessionBeforeOperation(
    onValidationComplete: (Boolean) -> Unit
) {
    validateSessionForOperation { result ->
        onValidationComplete(result == SessionValidationResult.VALID)
    }
}

/**
 * Example of how to integrate session workflow into existing screens.
 * This can be added to any screen that requires session validation.
 * 
 * Usage in existing Composables:
 * ```
 * @Composable
 * fun ExistingScreen() {
 *     // ... existing code ...
 *     
 *     // Add session workflow integration
 *     IntegrateSessionWorkflow(
 *         baseConfig = selectedBaseConfig,
 *         utilisateur = currentUser,
 *         onOperationValidated = {
 *             // Execute the operation that required session validation
 *             performBusinessOperation()
 *         }
 *     )
 *     
 *     // ... rest of existing code ...
 * }
 * ```
 */
@Composable
fun IntegrateSessionWorkflow(
    baseConfig: BaseConfig,
    utilisateur: Utilisateur,
    onOperationValidated: () -> Unit,
    sessionWorkflowViewModel: SessionWorkflowViewModel = hiltViewModel()
) {
    SessionWorkflowComposable(
        baseConfig = baseConfig,
        utilisateur = utilisateur,
        onSessionValidated = onOperationValidated,
        sessionWorkflowViewModel = sessionWorkflowViewModel
    )
}
